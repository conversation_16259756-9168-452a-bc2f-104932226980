"use client";

import React, { useState, useRef, useMemo, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Pagination,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  Chip,
  Select,
  SelectItem,
} from "@heroui/react"; // Assuming @heroui/react is the correct library
import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteSection,
} from "@heroui/autocomplete";
import { Icon } from "@iconify/react";
import { useQuery, useMutation } from "@apollo/client";

import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";

// Import hooks for data fetching

import { useUsersList } from "@/hooks/users/useUsersList";
import { useTeams } from "@/hooks/teams/useTeams";
import { useProjectUsers } from "@/hooks/users/useProjectUsers";
import {
  AllFieldsDocument,
  AllNotificationsDocument,
  FieldType,
  NotificationType,
  NotificationsNotificationTriggerConditionChoices,
  ExecuteCreateNotificationDocument,
  NotificationInput,
  useAllEmailTemplatesQuery,
  EmailTemplateType,
} from "@/graphql/schemas/generated";
import { phaseColors } from "@/components/primitives";
import { useRowCountStore } from "@/store/use-row-count-store";

// Component for the Notifications Management
interface NotificationsProps {
  isCreating: boolean;
  setIsCreating: (isCreating: boolean) => void;
  canEditConfiguration?: boolean; // Optional prop to control edit permissions
}

interface NotificationData {
  id: string;
  name: string;
  description?: string | null;
  leaderRecipients?: (string | null)[] | null;
  teamRecipients?: (string | null)[] | null;
  userRecipients?: (string | null)[] | null;
  roleRecipients?: (string | null)[] | null;
  allRecipients?: (string | null)[] | null; // For display purposes
  triggerCondition: NotificationsNotificationTriggerConditionChoices;
  value?: string | null;
  triggerField?: {
    id: string;
    name: string;
  } | null;
  emailTemplate?: {
    id: string;
    name: string;
  } | null;
  createdAt?: string | null;
}

// Field conditions - using GraphQL enum for notification trigger conditions
const conditionOptions = [
  {
    key: NotificationsNotificationTriggerConditionChoices.IsFilled,
    label: "Es rellenado",
  },
  {
    key: NotificationsNotificationTriggerConditionChoices.ItsValueIs,
    label: "Su valor es",
  },
  {
    key: NotificationsNotificationTriggerConditionChoices.Contains,
    label: "Contiene",
  },
  {
    key: NotificationsNotificationTriggerConditionChoices.Changes,
    label: "Cambia",
  },
];

export default function Notifications({
  isCreating,
  setIsCreating,
  canEditConfiguration = false, // Default to false if not provided
}: NotificationsProps) {
  const { rowCount } = useRowCountStore();

  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [filteredData, setFilteredData] = useState<NotificationData[]>([]);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Data fetching hooks
  const { users, fetchUsers } = useUsersList();
  const { teams, fetchTeams, teams: projects } = useTeams();
  const { projectUsers, fetchProjectUsers } = useProjectUsers();

  // GraphQL queries for fields, notifications, and email templates
  const { data: fieldsData, loading: fieldsLoading } =
    useQuery(AllFieldsDocument);
  const { data: notificationsData, loading: notificationsLoading } = useQuery(
    AllNotificationsDocument,
  );
  const { data: emailTemplatesData, loading: emailTemplatesLoading } =
    useAllEmailTemplatesQuery();

  // GraphQL mutation for creating notifications
  const [createNotification, { loading: createLoading }] = useMutation(
    ExecuteCreateNotificationDocument,
  );

  // State for selected recipients chips
  const [selectedRecipients, setSelectedRecipients] = useState<
    Array<{
      id: string;
      name: string;
      type: string;
    }>
  >([]);

  const getDateDisplayMapping = (): Record<string, string> => {
    const mapping: Record<string, string> = {};
    const uniqueDates = getUniqueValues("createdAt");

    uniqueDates.forEach((rawDate) => {
      if (rawDate && rawDate !== "") {
        try {
          const formattedDate = new Date(rawDate).toISOString().split("T")[0];

          mapping[rawDate] = formattedDate;
        } catch {
          mapping[rawDate] = rawDate; // Fallback to raw date if parsing fails
        }
      }
    });

    return mapping;
  };

  // Form states for create/edit
  const [newNotification, setNewNotification] = useState<
    Partial<NotificationData>
  >({
    name: "",
    // nameEn: "",
    description: "",
    // descriptionEn: "",
    leaderRecipients: [],
    teamRecipients: [],
    userRecipients: [],
    roleRecipients: [],
    triggerCondition: NotificationsNotificationTriggerConditionChoices.IsFilled,
    value: "",
    triggerField: null,
    emailTemplate: null,
  });

  // State for value input handling
  const [needsValueInput, setNeedsValueInput] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchUsers();
    fetchTeams();
    fetchProjectUsers();
  }, []);

  // Create recipients data structure from fetched data
  const recipientOptions = useMemo(() => {
    const options = [];

    // Users section
    if (users.length > 0) {
      options.push({
        key: "users",
        label: "Usuarios",
        items: users.map((user) => ({
          id: `u_${user.id}`,
          name: user.name,
          email: user.email,
          type: "Usuario",
        })),
      });
    }

    // Teams section
    if (teams.length > 0) {
      options.push({
        key: "teams",
        label: "Equipos",
        items: teams.map((team) => ({
          id: `t_${team.id}`,
          name: team.name,
          code: team.code,
          type: "Equipo",
        })),
      });
    }

    // Projects section
    if (teams.length > 0) {
      options.push({
        key: "projects",
        label: "Rol",
        items: projects.map((project) => ({
          id: `p_${project.id}`,
          name: project.name,
          code: project.code,
          type: "Proyecto",
        })),
      });
    }

    // Leaders section (from project users)
    if (projectUsers.length > 0) {
      const leaders = projectUsers.filter(
        (user) =>
          user.group_name.toLowerCase().includes("leader") ||
          user.group_name.toLowerCase().includes("líder"),
      );

      if (leaders.length > 0) {
        options.push({
          key: "leaders",
          label: "Líderes",
          items: leaders.map((leader) => ({
            id: `l_${leader.user_id}`,
            name: leader.name,
            email: leader.email,
            type: "Líder",
          })),
        });
      }
    }

    return options;
  }, [users, teams, projectUsers]);

  // Get field options from GraphQL data, grouped by type
  const fieldOptions = React.useMemo(() => {
    if (!fieldsData?.allFields) return [];

    return fieldsData.allFields.map((field: FieldType) => ({
      key: field.id,
      label: field.name,
      type: field.type,
      selectionOptions: field.selectionOptions,
      phase: field.subphase?.phase?.name || "default",
    }));
  }, [fieldsData]);

  // Get email template options from GraphQL data
  const emailTemplateOptions = React.useMemo(() => {
    if (!emailTemplatesData?.allEmailTemplates) return [];

    return emailTemplatesData.allEmailTemplates
      .filter((template): template is EmailTemplateType => template !== null)
      .map((template) => ({
        key: template.id,
        label: template.name,
        subject: template.subject,
        topic: template.topic,
      }));
  }, [emailTemplatesData]);

  // Group fields by type for sectioned autocomplete
  const fieldsByType = React.useMemo(() => {
    const grouped = fieldOptions.reduce(
      (acc: { [x: string]: any[] }, field: { type: string }) => {
        const type = field.type || "other";

        if (!acc[type]) {
          acc[type] = [];
        }

        acc[type].push(field);

        return acc;
      },
      {} as Record<string, typeof fieldOptions>,
    );

    return grouped;
  }, [fieldOptions]);

  // Get the selected field details
  const selectedField = React.useMemo(() => {
    return fieldOptions.find(
      (field: { key: string }) =>
        field.key === newNotification.triggerField?.id,
    );
  }, [fieldOptions, newNotification.triggerField?.id]);

  // Status options for task and document fields
  const taskStatusOptions = [
    { key: "pendiente", label: "Pendiente" },
    { key: "en_progreso", label: "En progreso" },
    { key: "completado", label: "Completado" },
    { key: "cancelado", label: "Cancelado" },
  ];

  // Get value options based on field type and condition
  const getValueOptions = React.useMemo(() => {
    if (!selectedField || !needsValueInput) return [];

    const fieldType = selectedField.type;

    // For task and document fields, return status options
    if (fieldType === "TASK" || fieldType === "DOCUMENT") {
      return taskStatusOptions;
    }

    // For selection fields, parse and return selection options
    if (fieldType === "SELECTION" && selectedField.selectionOptions) {
      try {
        const options = JSON.parse(selectedField.selectionOptions);

        if (Array.isArray(options)) {
          return options.map((option: any, index: number) => ({
            key: option.text || `option_${index}`,
            label: option.text || `Opción ${index + 1}`,
          }));
        }
      } catch {
        // Log error for debugging but don't break the UI
      }
    }

    return [];
  }, [selectedField, needsValueInput]);

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedNotification, setSelectedNotification] =
    useState<NotificationData | null>(null);

  // Transform GraphQL data to table data
  const tableData = React.useMemo(() => {
    if (!notificationsData?.allNotifications) return [];

    return notificationsData.allNotifications
      .filter(
        (notification: unknown): notification is NotificationType =>
          notification !== null,
      )
      .map(
        (notification: NotificationType): NotificationData => ({
          id: notification.id,
          name: notification.name,
          description: notification.description,
          leaderRecipients: notification.leaderRecipients,
          teamRecipients: notification.teamRecipients,
          userRecipients: notification.userRecipients,
          roleRecipients: notification.roleRecipients,
          allRecipients: notification.allRecipients,
          triggerCondition: notification.triggerCondition,
          value: notification.value,
          triggerField: notification.triggerField,
          emailTemplate: notification.emailTemplate
            ? {
                id: notification.emailTemplate.id,
                name: notification.emailTemplate.name,
              }
            : null,
          // isExternal: notification.isExternal,
          createdAt: notification.createdAt,
        }),
      );
  }, [notificationsData]);

  // Helper functions for filtering and sorting
  const getUniqueValues = (column: keyof NotificationData): string[] => {
    return Array.from(
      new Set(
        tableData.map((notification: { [x: string]: any }) => {
          const value = notification[column];

          if (value === null || value === undefined) return "Sin valor";
          if (Array.isArray(value))
            return value.length > 0 ? "Con destinatarios" : "Sin destinatarios";
          if (typeof value === "object" && value !== null) {
            return (value as any).name || "Sin nombre";
          }

          return String(value);
        }),
      ),
    );
  };

  const handleFilterChange = (column: string, selectedValues: string[]) => {
    const newFilters = {
      ...activeFilters,
      [column]: selectedValues,
    };

    if (selectedValues.length === 0) {
      delete newFilters[column];
    }

    setActiveFilters(newFilters);
    setPage(1); // Reset to first page when filtering
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  const applyFiltersAndSort = (
    term: string,
    filters: Record<string, string[]>,
    sort: { column: string; direction: "asc" | "desc" } | null,
  ) => {
    let filtered = tableData;

    // Apply search term filter
    if (term) {
      filtered = filtered.filter(
        (notification: {
          name: string;
          description: string;
          triggerField: { name: string };
          emailTemplate: { name: string };
        }) =>
          notification.name.toLowerCase().includes(term.toLowerCase()) ||
          (notification.description &&
            notification.description
              .toLowerCase()
              .includes(term.toLowerCase())) ||
          (notification.triggerField?.name &&
            notification.triggerField.name
              .toLowerCase()
              .includes(term.toLowerCase())) ||
          (notification.emailTemplate?.name &&
            notification.emailTemplate.name
              .toLowerCase()
              .includes(term.toLowerCase())),
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((notification: { [x: string]: any }) => {
          const value = notification[column as keyof NotificationData];
          let stringValue: string;

          if (value === null || value === undefined) {
            stringValue = "Sin valor";
          } else if (Array.isArray(value)) {
            stringValue =
              value.length > 0 ? "Con destinatarios" : "Sin destinatarios";
          } else if (typeof value === "object" && value !== null) {
            stringValue = (value as any).name || "Sin nombre";
          } else {
            stringValue = String(value);
          }

          return values.includes(stringValue);
        });
      }
    });

    // Apply sorting
    if (sort) {
      filtered.sort((a: { [x: string]: any }, b: { [x: string]: any }) => {
        const aValue = a[sort.column as keyof NotificationData];
        const bValue = b[sort.column as keyof NotificationData];

        let aString = "";
        let bString = "";

        if (typeof aValue === "string") aString = aValue;
        else if (typeof aValue === "object" && aValue !== null)
          aString = (aValue as any).name || "";
        else aString = String(aValue || "");

        if (typeof bValue === "string") bString = bValue;
        else if (typeof bValue === "object" && bValue !== null)
          bString = (bValue as any).name || "";
        else bString = String(bValue || "");

        const comparison = aString.localeCompare(bString);

        return sort.direction === "asc" ? comparison : -comparison;
      });
    }

    setFilteredData(filtered);
  };

  // Effect to apply filters and sort when dependencies change
  useEffect(() => {
    applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
  }, [tableData, searchTerm, activeFilters, sortConfig]);

  // Effect to check if the selected condition needs a value input
  useEffect(() => {
    const valueNeededConditions = [
      NotificationsNotificationTriggerConditionChoices.ItsValueIs,
      NotificationsNotificationTriggerConditionChoices.Contains,
    ];

    setNeedsValueInput(
      valueNeededConditions.includes(newNotification.triggerCondition as any),
    );
  }, [newNotification.triggerCondition]);

  const pages = Math.ceil(filteredData.length / rowCount);
  const items = useMemo(() => {
    const start = (page - 1) * rowCount;
    const end = start + rowCount;

    return filteredData.slice(start, end);
  }, [page, filteredData]);

  const handleRowClick = (notification: NotificationData) => {
    setSelectedNotification(notification);
    setIsViewModalOpen(true);
  };

  const handleEdit = (notification: NotificationData) => {
    setSelectedNotification(notification);
    setNewNotification({
      name: notification.name,
      description: notification.description,
      leaderRecipients: notification.leaderRecipients,
      teamRecipients: notification.teamRecipients,
      userRecipients: notification.userRecipients,
      roleRecipients: notification.roleRecipients,
      triggerCondition: notification.triggerCondition,
      value: notification.value,
      triggerField: notification.triggerField,
      emailTemplate: notification.emailTemplate,
      // isExternal: notification.isExternal,
    });

    // Populate selected recipients chips for editing from allRecipients
    const editRecipients: Array<{ id: string; name: string; type: string }> =
      [];

    notification.allRecipients?.forEach((recipientValue) => {
      if (!recipientValue) return;

      // Find the recipient by matching email/name across all sections
      for (const section of recipientOptions) {
        const found = section.items.find((item) => {
          if (section.key === "users" && "email" in item) {
            return item.email === recipientValue;
          } else if (section.key === "teams") {
            return item.name === recipientValue;
          } else if (section.key === "leaders" && "email" in item) {
            return item.email === recipientValue;
          } else if (section.key === "projects") {
            return item.name === recipientValue;
          }

          return false;
        });

        if (found) {
          editRecipients.push({
            id: found.id,
            name: found.name,
            type: found.type,
          });
          break;
        }
      }
    });
    setSelectedRecipients(editRecipients);

    setIsEditModalOpen(true);
  };

  const handleDelete = (notification: NotificationData) => {
    setSelectedNotification(notification);
    setIsDeleteModalOpen(true);
  };

  // Helper function to add recipient chip
  const addRecipientChip = (recipientId: string) => {
    // Find the recipient in the options
    let foundRecipient = null;

    for (const section of recipientOptions) {
      const found = section.items.find((item) => item.id === recipientId);

      if (found) {
        foundRecipient = {
          id: found.id,
          name: found.name,
          type: found.type,
        };
        break;
      }
    }

    if (
      foundRecipient &&
      !selectedRecipients.find((r) => r.id === recipientId)
    ) {
      const updatedRecipients = [...selectedRecipients, foundRecipient];

      setSelectedRecipients(updatedRecipients);

      // Update form state with separate recipient arrays
      const userRecipients: string[] = [];
      const teamRecipients: string[] = [];
      const leaderRecipients: string[] = [];
      const roleRecipients: string[] = [];

      updatedRecipients.forEach((recipient) => {
        if (recipient.id.startsWith("u_")) {
          userRecipients.push(recipient.id);
        } else if (recipient.id.startsWith("t_")) {
          teamRecipients.push(recipient.id);
        } else if (recipient.id.startsWith("l_")) {
          leaderRecipients.push(recipient.id);
        } else if (recipient.id.startsWith("p_")) {
          roleRecipients.push(recipient.id);
        }
      });

      setNewNotification({
        ...newNotification,
        userRecipients,
        teamRecipients,
        leaderRecipients,
        roleRecipients,
      });
    }
  };

  // Helper function to remove recipient chip
  const removeRecipientChip = (recipientId: string) => {
    const updatedRecipients = selectedRecipients.filter(
      (r) => r.id !== recipientId,
    );

    setSelectedRecipients(updatedRecipients);

    // Update form state with separate recipient arrays
    const userRecipients: string[] = [];
    const teamRecipients: string[] = [];
    const leaderRecipients: string[] = [];
    const roleRecipients: string[] = [];

    updatedRecipients.forEach((recipient) => {
      if (recipient.id.startsWith("u_")) {
        userRecipients.push(recipient.id);
      } else if (recipient.id.startsWith("t_")) {
        teamRecipients.push(recipient.id);
      } else if (recipient.id.startsWith("l_")) {
        leaderRecipients.push(recipient.id);
      } else if (recipient.id.startsWith("p_")) {
        roleRecipients.push(recipient.id);
      }
    });

    setNewNotification({
      ...newNotification,
      userRecipients,
      teamRecipients,
      leaderRecipients,
      roleRecipients,
    });
  };

  // Helper function to get label from key for conditions
  const getLabelFromKey = (
    key: string | undefined,
    optionsArray: { key: string; label: string }[],
  ) => {
    if (!key) return "";
    const option = optionsArray.find((item) => item.key === key);

    return option ? option.label : key;
  };

  // Helper function to get phase color
  const getPhaseColor = (phase: string) => {
    const phaseKey = phase.toLowerCase() as keyof typeof phaseColors;
    const color = phaseColors[phaseKey] || phaseColors.default;

    return {
      backgroundColor: color.bg,
      color: color.text,
      borderColor: color.border,
    };
  };

  // Helper function to get chip color based on recipient type
  const getRecipientChipColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "usuario":
        return "primary";
      case "equipo":
        return "secondary";
      case "líder":
        return "warning";
      case "proyecto":
        return "success";
      default:
        return "default";
    }
  };

  // Helper function to check if form is valid for creation
  const isCreateFormValid = React.useMemo(() => {
    const hasRecipients =
      (newNotification.userRecipients &&
        newNotification.userRecipients.length > 0) ||
      (newNotification.teamRecipients &&
        newNotification.teamRecipients.length > 0) ||
      (newNotification.leaderRecipients &&
        newNotification.leaderRecipients.length > 0) ||
      (newNotification.roleRecipients &&
        newNotification.roleRecipients.length > 0);

    return !!(
      newNotification.name &&
      newNotification.triggerField?.id &&
      newNotification.triggerCondition &&
      hasRecipients &&
      newNotification.emailTemplate?.id
    );
  }, [newNotification]);

  React.useEffect(() => {
    if (isCreating) {
      setNewNotification({
        name: "",
        description: "",
        leaderRecipients: [],
        teamRecipients: [],
        userRecipients: [],
        roleRecipients: [],
        triggerCondition:
          NotificationsNotificationTriggerConditionChoices.IsFilled,
        value: "",
        triggerField: null,
        emailTemplate: null,
        // isExternal: false,
      });
      setSelectedRecipients([]);
      setIsCreateModalOpen(true);
    } else {
      setIsCreateModalOpen(false);
    }
  }, [isCreating]);

  const handleSaveCreate = async () => {
    try {
      // Validate required fields
      if (
        !newNotification.name ||
        !newNotification.triggerField?.id ||
        !newNotification.triggerCondition ||
        !newNotification.emailTemplate?.id
      ) {
        console.error("Missing required fields for notification creation");

        return;
      }

      // Separate recipients by type
      const userRecipients: string[] = [];
      const teamRecipients: string[] = [];
      const leaderRecipients: string[] = [];
      const roleRecipients: string[] = [];

      selectedRecipients.forEach((recipient) => {
        const id = recipient.id;

        // For users (starts with u_)
        if (id.startsWith("u_")) {
          // Remove the u_ prefix and add the actual user ID
          const actualUserId = id.substring(2); // Remove "u_" prefix

          userRecipients.push(actualUserId);
        }
        // For teams (starts with t_)
        else if (id.startsWith("t_")) {
          // Remove the t_ prefix and add the actual team ID
          const actualTeamId = id.substring(2); // Remove "t_" prefix

          teamRecipients.push(actualTeamId);
        }
        // For leaders (starts with l_)
        else if (id.startsWith("l_")) {
          // Remove the l_ prefix and add the actual leader ID
          const actualLeaderId = id.substring(2); // Remove "l_" prefix

          leaderRecipients.push(actualLeaderId);
        } else if (id.startsWith("p_")) {
          // Remove the p_ prefix and add the actual project ID
          const actualProjectId = id.substring(2); // Remove "p_" prefix

          roleRecipients.push(actualProjectId);
        }
      });

      // Prepare the notification data
      const notificationData: NotificationInput = {
        name: newNotification.name,
        // nameEn: newNotification.nameEn || "",
        description: newNotification.description || "",
        // descriptionEn: newNotification.descriptionEn || "",
        userRecipients,
        teamRecipients,
        leaderRecipients,
        roleRecipients,
        triggerFieldId: newNotification.triggerField.id,
        triggerCondition: newNotification.triggerCondition,
        value: newNotification.value || "",
        emailTemplateId: newNotification.emailTemplate.id,
        // isExternal: newNotification.isExternal || false,
      };

      console.log("Creating notification with data:", notificationData);

      // Execute the GraphQL mutation
      const result = await createNotification({
        variables: {
          notificationData,
        },
        refetchQueries: [{ query: AllNotificationsDocument }],
      });

      // Check for successful response
      if (result.data?.createNotification?.notification) {
        console.log(
          "Notification created successfully:",
          result.data.createNotification.notification,
        );
        setIsCreateModalOpen(false);
        setIsCreating(false);

        // Reset form
        setNewNotification({
          name: "",
          description: "",
          leaderRecipients: [],
          teamRecipients: [],
          userRecipients: [],
          roleRecipients: [],
          triggerCondition:
            NotificationsNotificationTriggerConditionChoices.IsFilled,
          value: "",
          triggerField: null,
          emailTemplate: null,
          // isExternal: false,
        });
        setSelectedRecipients([]);
      }
    } catch (error) {
      console.error("Error creating notification:", error);
      // Handle error (could show an error message)
    }
  };

  const handleSaveEdit = () => {
    if (!selectedNotification) return;
    // TODO: Implement with GraphQL updateNotification mutation
    console.log("Update notification:", newNotification);
    setIsEditModalOpen(false);
    setSelectedNotification(null);
  };

  const handleConfirmDelete = () => {
    if (!selectedNotification) return;
    // TODO: Implement with GraphQL deleteNotification mutation
    console.log("Delete notification:", selectedNotification.id);
    setIsDeleteModalOpen(false);
    setSelectedNotification(null);
  };

  // Search and filter handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page when searching
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setActiveFilters({});
    setPage(1);
  };

  // Simple textarea input handler
  const handleTextareaInput = (value: string) => {
    setNewNotification({ ...newNotification, description: value });
  };

  const closeCreateEditModal = () => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsCreating(false);
    setSelectedNotification(null);
    setSelectedRecipients([]);
    setNewNotification({
      name: "",
      description: "",
      leaderRecipients: [],
      teamRecipients: [],
      userRecipients: [],
      roleRecipients: [],
      triggerCondition:
        NotificationsNotificationTriggerConditionChoices.IsFilled,
      value: "",
      triggerField: null,
      emailTemplate: null,
      // isExternal: false,
    });
  };

  return (
    <div className="pt-4 w-full">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
        <div className="flex gap-2 w-full">
          <Input
            isClearable
            className="w-full"
            placeholder="Buscar por nombre, descripción, campo o plantilla..."
            startContent={
              <Icon
                className="text-default-400"
                icon="lucide:search"
                width={18}
              />
            }
            value={searchTerm}
            onClear={() => setSearchTerm("")}
            onValueChange={handleSearch}
          />
        </div>
        {/* <div className="flex gap-2 w-full sm:w-auto justify-end">
          <Button
            color={
              searchTerm || Object.keys(activeFilters).length > 0
                ? "primary"
                : "default"
            }
            variant="flat"
            onPress={handleClearFilters}
          >
            Borrar filtros{" "}
            {(searchTerm || Object.keys(activeFilters).length > 0) &&
              `(${(searchTerm ? 1 : 0) + Object.keys(activeFilters).length})`}
          </Button>
        </div> */}
      </div>

      <Table
        key={canEditConfiguration ? "edit" : "view"}
        removeWrapper
        aria-label="Tabla de notificaciones"
        bottomContent={
          pages > 0 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={(newPage) => setPage(newPage)}
              />
            </div>
          ) : null
        }
      >
        <TableHeader>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="id"
              items={getUniqueValues("id")}
              sortConfig={sortConfig}
              title="#"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="description"
              items={getUniqueValues("description")}
              sortConfig={sortConfig}
              title="Descripción"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="createdAt"
              displayText={getDateDisplayMapping()}
              items={getUniqueValues("createdAt")}
              sortConfig={sortConfig}
              title="Fecha de creación"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Acciones</TableColumn>
        </TableHeader>
        <TableBody
          emptyContent={"No hay notificaciones para mostrar."}
          isLoading={notificationsLoading}
          items={items}
        >
          {(item) => {
            const notification = item as NotificationData;

            return (
              <TableRow
                key={notification.id}
                className="cursor-pointer hover:bg-default-100"
                onClick={() => handleRowClick(notification)}
              >
                <TableCell>{notification.id}</TableCell>
                <TableCell>{notification.name}</TableCell>
                <TableCell>
                  {notification.description || "Sin descripción"}
                </TableCell>
                <TableCell>
                  {notification.createdAt
                    ? new Date(notification.createdAt)
                        .toISOString()
                        .split("T")[0]
                    : "Sin fecha"}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      isIconOnly
                      color="primary"
                      isDisabled={!canEditConfiguration}
                      size="sm"
                      variant="flat"
                      onPress={() => handleEdit(notification)}
                    >
                      <Icon className="text-lg" icon="lucide:edit-3" />
                    </Button>
                    <Button
                      isIconOnly
                      color="danger"
                      isDisabled={!canEditConfiguration}
                      size="sm"
                      variant="flat"
                      onPress={() => handleDelete(notification)}
                    >
                      <Icon className="text-lg" icon="lucide:trash" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          }}
        </TableBody>
      </Table>

      {/* View Notification Modal */}
      <Modal
        isOpen={isViewModalOpen}
        size="3xl"
        onOpenChange={() => setIsViewModalOpen(false)}
      >
        <ModalContent>
          {(_onClose) => (
            <>
              <ModalHeader>
                <h3 className="text-xl font-bold">
                  Detalles de la Notificación
                </h3>
              </ModalHeader>
              <ModalBody>
                {selectedNotification && (
                  <div className="space-y-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm text-default-500">Nombre</span>
                      <span className="text-lg font-medium">
                        {selectedNotification.name}
                      </span>
                    </div>

                    <div className="flex flex-col space-y-1">
                      <span className="text-sm text-default-500">
                        Descripción
                      </span>
                      <span>{selectedNotification.description}</span>
                    </div>

                    <div className="flex flex-col space-y-1">
                      <span className="text-sm text-default-500">
                        Destinatarios
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {selectedNotification.allRecipients &&
                        selectedNotification.allRecipients.length > 0 ? (
                          selectedNotification.allRecipients.map(
                            (recipientValue, index) => {
                              if (!recipientValue) return null;

                              // Find recipient by matching email/name across all sections
                              let recipientInfo = null;

                              for (const section of recipientOptions) {
                                const found = section.items.find((item) => {
                                  if (
                                    section.key === "users" &&
                                    "email" in item
                                  ) {
                                    return item.email === recipientValue;
                                  } else if (section.key === "teams") {
                                    return item.name === recipientValue;
                                  } else if (
                                    section.key === "leaders" &&
                                    "email" in item
                                  ) {
                                    return item.email === recipientValue;
                                  } else if (section.key === "projects") {
                                    return item.name === recipientValue;
                                  }

                                  return false;
                                });

                                if (found) {
                                  recipientInfo = found;
                                  break;
                                }
                              }

                              if (recipientInfo) {
                                return (
                                  <Chip
                                    key={`${recipientValue}-${index}`}
                                    color={getRecipientChipColor(
                                      recipientInfo.type,
                                    )}
                                    size="sm"
                                    variant="flat"
                                  >
                                    {recipientInfo.name} ({recipientInfo.type})
                                  </Chip>
                                );
                              }

                              return (
                                <Chip
                                  key={`${recipientValue}-${index}`}
                                  color="default"
                                  size="sm"
                                  variant="flat"
                                >
                                  {recipientValue}
                                </Chip>
                              );
                            },
                          )
                        ) : (
                          <span className="text-sm text-default-400">
                            No hay destinatarios seleccionados
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col space-y-1">
                      <span className="text-sm text-default-500">
                        Regla de notificación
                      </span>
                      <div className="p-3 bg-default-50 rounded-md">
                        <p>
                          <span className="font-medium">Si el campo </span>
                          <span className="text-primary">
                            {selectedNotification.triggerField?.name ||
                              "Campo no encontrado"}
                          </span>
                          <span className="font-medium"> </span>
                          <span className="text-primary">
                            {getLabelFromKey(
                              selectedNotification.triggerCondition,
                              conditionOptions,
                            )}
                          </span>
                          {selectedNotification.value && (
                            <>
                              <span className="font-medium"> </span>
                              <span className="text-primary">
                                &ldquo;{selectedNotification.value}
                                &rdquo;
                              </span>
                            </>
                          )}
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-1">
                      <span className="text-sm text-default-500">
                        Plantilla de Email
                      </span>
                      <span>
                        {selectedNotification.emailTemplate?.name ||
                          "Sin plantilla asignada"}
                      </span>
                    </div>
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button
                  color="primary"
                  onPress={() => setIsViewModalOpen(false)}
                >
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Create/Edit Notification Modal (shared structure) */}
      <Modal
        isOpen={isCreateModalOpen || isEditModalOpen}
        scrollBehavior="inside"
        size="3xl"
        onOpenChange={closeCreateEditModal}
      >
        <ModalContent>
          {(_onClose) => (
            <>
              <ModalHeader>
                <h3 className="text-xl font-bold">
                  {isEditModalOpen
                    ? "Editar Notificación"
                    : "Crear Nueva Notificación"}
                </h3>
              </ModalHeader>
              <ModalBody>
                <div className="flex flex-col gap-6">
                  <Input
                    label="Nombre de la Notificación"
                    placeholder="Ingrese el nombre de la notificación"
                    value={newNotification.name}
                    onValueChange={(value) =>
                      setNewNotification({ ...newNotification, name: value })
                    }
                  />

                  {/* <Input
                    isDisabled={true}
                    label="Nombre de la Notificación (English)"
                    placeholder="Ingrese el nombre de la notificación en inglés"
                    // value={newNotification.nameEn}
                    // onValueChange={(value) =>
                    //   setNewNotification({ ...newNotification, nameEn: value })
                    // }
                  /> */}

                  <Textarea
                    ref={textareaRef}
                    label="Descripción de la Notificación"
                    minRows={3}
                    placeholder="Ingrese la descripción de la notificación"
                    value={newNotification.description || ""}
                    onValueChange={handleTextareaInput}
                  />

                  {/* <Input
                    isDisabled={true}
                    label="Descripción de la Notificación (Inglés)"
                    placeholder="Ingrese la descripción de la notificación en inglés"
                    // value={newNotification.descriptionEn}
                    // onValueChange={(value) =>
                    //   setNewNotification({
                    //     ...newNotification,
                    //     descriptionEn: value,
                    //   })
                    // }
                  /> */}

                  {/* Destinatarios - Autocomplete Component */}
                  <div>
                    <label
                      className="block text-small font-medium pb-1.5"
                      htmlFor="recipients-autocomplete"
                    >
                      Destinatarios
                    </label>

                    {/* Selected Recipients Chips */}
                    {selectedRecipients.length > 0 && (
                      <div className="mb-3 flex flex-wrap gap-2">
                        {selectedRecipients.map((recipient) => (
                          <Chip
                            key={recipient.id}
                            color={getRecipientChipColor(recipient.type)}
                            variant="flat"
                            onClose={() => removeRecipientChip(recipient.id)}
                          >
                            {recipient.name} ({recipient.type})
                          </Chip>
                        ))}
                      </div>
                    )}

                    <div className="mb-2">
                      <Autocomplete
                        className="max-w-full"
                        id="recipients-autocomplete"
                        label="Seleccionar destinatarios"
                        placeholder="Buscar usuarios, equipos o líderes"
                        variant="bordered"
                        onSelectionChange={(key) => {
                          if (key) {
                            addRecipientChip(key as string);
                          }
                        }}
                      >
                        {recipientOptions.map((section) => (
                          <AutocompleteSection
                            key={section.key}
                            title={section.label}
                          >
                            {section.items.map((item) => (
                              <AutocompleteItem
                                key={item.id}
                                textValue={`${item.name} (${item.type})`}
                              >
                                <div className="text-default-700">
                                  {item.name} ({item.type})
                                </div>
                              </AutocompleteItem>
                            ))}
                          </AutocompleteSection>
                        ))}
                      </Autocomplete>
                    </div>
                  </div>

                  {/* Email Template Selection */}
                  <div>
                    <Autocomplete
                      label="Plantilla de Email"
                      placeholder="Seleccionar plantilla de email"
                      selectedKey={newNotification.emailTemplate?.id}
                      variant="bordered"
                      onSelectionChange={(key) => {
                        const selectedTemplate = emailTemplateOptions.find(
                          (template) => template.key === key,
                        );

                        setNewNotification({
                          ...newNotification,
                          emailTemplate: selectedTemplate
                            ? {
                                id: selectedTemplate.key,
                                name: selectedTemplate.label,
                              }
                            : null,
                        });
                      }}
                    >
                      {emailTemplateOptions.map((template) => (
                        <AutocompleteItem
                          key={template.key}
                          textValue={template.label}
                        >
                          <div className="text-default-700">
                            <div className="font-medium">{template.label}</div>
                            <div className="text-sm text-default-500">
                              {template.subject}
                            </div>
                            {template.topic && (
                              <div className="text-xs text-default-400">
                                Tópico: {template.topic}
                              </div>
                            )}
                          </div>
                        </AutocompleteItem>
                      ))}
                    </Autocomplete>
                  </div>

                  {/* Rule Condition */}
                  <div className="border p-4 rounded-lg bg-default-50">
                    <h4 className="font-medium mb-3">
                      Enviar notificación si:
                    </h4>

                    <div className="flex flex-wrap items-center gap-2 rounded-lg">
                      <span className="font-medium">SI</span>
                      <Autocomplete
                        className="w-[275px]"
                        placeholder="Campo"
                        selectedKey={newNotification.triggerField?.id}
                        variant="bordered"
                        onSelectionChange={(key) => {
                          const selectedField = fieldOptions.find(
                            (f: { key: string | number }) => f.key === key,
                          );

                          setNewNotification({
                            ...newNotification,
                            triggerField: selectedField
                              ? {
                                  id: selectedField.key,
                                  name: selectedField.label,
                                }
                              : null,
                          });
                        }}
                      >
                        {Object.entries(fieldsByType).map(([type, fields]) => (
                          <AutocompleteSection
                            key={type}
                            title={
                              {
                                informative: "Informativo",
                                selection: "Selección",
                                task_with_subtasks: "Subtarea",
                                task: "Tarea",
                                document: "Documento",
                              }[type] ||
                              type.charAt(0).toUpperCase() + type.slice(1)
                            }
                          >
                            {(fields as typeof fieldOptions).map(
                              (option: {
                                key: React.Key | null | undefined;
                                label:
                                  | string
                                  | number
                                  | bigint
                                  | boolean
                                  | React.ReactElement<
                                      any,
                                      string | React.JSXElementConstructor<any>
                                    >
                                  | Iterable<React.ReactNode>
                                  | Promise<React.AwaitedReactNode>
                                  | null
                                  | undefined;
                              }) => (
                                <AutocompleteItem
                                  key={option.key}
                                  textValue={String(option.label)}
                                >
                                  <div className="text-default-700">
                                    {option.label}{" "}
                                    <Chip
                                      size="sm"
                                      style={getPhaseColor(
                                        (option as any).phase,
                                      )}
                                      variant="flat"
                                    >
                                      {(option as any).phase}
                                    </Chip>
                                  </div>
                                </AutocompleteItem>
                              ),
                            )}
                          </AutocompleteSection>
                        ))}
                      </Autocomplete>

                      <Select
                        className="w-[250px]"
                        placeholder="Condición"
                        selectedKeys={
                          newNotification.triggerCondition
                            ? [newNotification.triggerCondition]
                            : []
                        }
                        onChange={(e) =>
                          setNewNotification({
                            ...newNotification,
                            triggerCondition: e.target
                              .value as NotificationsNotificationTriggerConditionChoices,
                          })
                        }
                      >
                        {conditionOptions.map((option) => (
                          <SelectItem key={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </Select>

                      {needsValueInput && (
                        <>
                          {getValueOptions.length > 0 ? (
                            <Select
                              className="w-[150px]"
                              placeholder="Valor"
                              selectedKeys={
                                newNotification.value
                                  ? [newNotification.value]
                                  : []
                              }
                              onChange={(e) =>
                                setNewNotification({
                                  ...newNotification,
                                  value: e.target.value,
                                })
                              }
                            >
                              {getValueOptions.map((option) => (
                                <SelectItem key={option.key}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </Select>
                          ) : (
                            <Input
                              className="w-[150px]"
                              placeholder="Valor"
                              value={newNotification.value || ""}
                              onValueChange={(value) =>
                                setNewNotification({
                                  ...newNotification,
                                  value,
                                })
                              }
                            />
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="danger"
                  variant="light"
                  onPress={closeCreateEditModal}
                >
                  Cancelar
                </Button>
                <Button
                  color="primary"
                  isDisabled={!isEditModalOpen && !isCreateFormValid}
                  isLoading={!isEditModalOpen && createLoading}
                  onPress={isEditModalOpen ? handleSaveEdit : handleSaveCreate}
                >
                  {isEditModalOpen ? "Guardar Cambios" : "Crear Notificación"}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Delete Notification Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onOpenChange={() => {
          setIsDeleteModalOpen(false);
          setSelectedNotification(null);
        }}
      >
        <ModalContent>
          {(_onClose) => (
            <>
              <ModalHeader>
                <h3 className="text-xl font-bold">Confirmar Eliminación</h3>
              </ModalHeader>
              <ModalBody>
                <p>
                  ¿Está seguro de que desea eliminar la notificación{" "}
                  <span className="font-semibold">
                    {selectedNotification?.name}
                  </span>
                  ?
                  <span className="block mt-2 text-gray-600 text-sm">
                    Esta acción no se puede deshacer.
                  </span>
                </p>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="default"
                  variant="flat"
                  onPress={() => {
                    setIsDeleteModalOpen(false);
                    setSelectedNotification(null);
                  }}
                >
                  Cancelar
                </Button>
                <Button color="danger" onPress={handleConfirmDelete}>
                  Eliminar Notificación
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
}
